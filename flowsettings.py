import os
import logging
from importlib.metadata import version
from inspect import currentframe, getframeinfo

from decouple import config
from datetime import datetime
from logging.handlers import TimedRotatingFileHandler
from theflow.settings.default import *  # noqa

cur_frame = currentframe()
if cur_frame is None:
    raise ValueError("Cannot get the current frame.")
this_file = getframeinfo(cur_frame).filename
this_dir = Path(this_file).parent

RAG_DATA_DIR = this_dir / "app_data"
RAG_DATA_DIR.mkdir(parents=True, exist_ok=True)

RAG_MODEL_DIR = this_dir / "local_models"
RAG_MODEL_DIR.mkdir(parents=True, exist_ok=True)

# User data directory
RAG_USER_DATA_DIR = RAG_DATA_DIR / "user_data"
RAG_USER_DATA_DIR.mkdir(parents=True, exist_ok=True)

# chunks output directory
RAG_CHUNKS_OUTPUT_DIR = RAG_DATA_DIR / "chunks_cache_dir"
RAG_CHUNKS_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

# zip output directory
RAG_ZIP_OUTPUT_DIR = RAG_DATA_DIR / "zip_cache_dir"
RAG_ZIP_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

# zip input directory
RAG_ZIP_INPUT_DIR = RAG_DATA_DIR / "zip_cache_dir_in"
RAG_ZIP_INPUT_DIR.mkdir(parents=True, exist_ok=True)

# 保存模型的地址
os.environ["HF_HOME"] = str(RAG_DATA_DIR / "huggingface")
os.environ["HF_HUB_CACHE"] = str(RAG_DATA_DIR / "huggingface")


def setup_logging():
    # 使用全局变量来追踪初始化状态
    global _logging_initialized
    if globals().get('_logging_initialized', False):
        return logging.getLogger()

    # 读取日志配置
    global_log_level = config('LOG_LEVEL', default='DEBUG').upper()
    log_file_level = config('LOG_FILE_LEVEL', default='DEBUG').upper()  # 改为DEBUG级别
    log_file_path = config('LOG_FILE_PATH', default='./app_data/logs/')

    # 确保日志目录存在
    if not os.path.exists(log_file_path):
        os.makedirs(log_file_path)

    # 重置所有现有的logger配置
    logging.root.manager.loggerDict.clear()

    # 清除root logger的所有处理器
    root_logger = logging.getLogger()
    root_logger.handlers.clear()

    # 设置日志格式
    log_formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")

    # 设置控制台日志处理程序
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, global_log_level))
    console_handler.setFormatter(log_formatter)

    # 设置文件日志处理程序
    file_handler = TimedRotatingFileHandler(
        filename=os.path.join(log_file_path, datetime.now().strftime('%Y%m%d.log')),
        when='midnight',
        interval=1,
        backupCount=365,
        encoding='utf-8'
    )
    file_handler.setLevel(getattr(logging, log_file_level))
    file_handler.setFormatter(log_formatter)

    # 配置根日志记录器
    root_logger.setLevel(getattr(logging, global_log_level))
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)

    # 设置特定库的日志级别
    logging.getLogger('aiosqlite').setLevel(getattr(logging, log_file_level))
    logging.getLogger('asyncio').setLevel(getattr(logging, log_file_level))
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger('pdfminer').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('httpcore').setLevel(logging.WARNING)

    # 标记日志系统已初始化
    global _logging_initialized
    _logging_initialized = True

    # 确保 API 包及其所有子模块的日志级别正确
    for logger_name in logging.root.manager.loggerDict:
        if logger_name.startswith('API'):
            module_logger = logging.getLogger(logger_name)
            module_logger.setLevel(getattr(logging, global_log_level))

    # 防止其他地方修改 root logger 级别
    def protect_root_level(level):
        if hasattr(protect_root_level, 'original_level'):
            return protect_root_level.original_level
        return level

    root_logger.setLevel = protect_root_level
    protect_root_level.original_level = root_logger.level

    return root_logger


# 在模块级别调用setup_logging
root_logger = setup_logging()

# doc directory
RAG_DOC_DIR = this_dir / "docs"

RAG_MODE = "dev"
RAG_FEATURE_USER_MANAGEMENT = config(
    "RAG_FEATURE_USER_MANAGEMENT", default=True, cast=bool
)
RAG_USER_CAN_SEE_PUBLIC = None

RAG_ENABLE_ALEMBIC = False
RAG_DATABASE_SYNC = f"sqlite:///{RAG_USER_DATA_DIR / 'sql.db'}"
RAG_DATABASE_ASYNC = f"sqlite+aiosqlite:///{RAG_USER_DATA_DIR / 'sql.db'}"
RAG_FILESTORAGE_PATH = str(RAG_USER_DATA_DIR / "files")

RAG_DOCSTORE = {
    "__type__": "RAG.storages.SimpleFileDocumentStore",
    "path": str(RAG_USER_DATA_DIR / "docstore"),
}
RAG_VECTORSTORE = {
    "__type__": "RAG.storages.MilvusVectorStore",
    "path": str(RAG_USER_DATA_DIR / "vectorstore"),
}

# Milvus 数据库配置读取自 .env 文件
MILVUS_HOST = config('MILVUS_HOST', default='localhost')
MILVUS_PORT = config('MILVUS_PORT', default='19530', cast=int)
MILVUS_USER = config('MILVUS_USER', default=None)
MILVUS_PASSWORD = config('MILVUS_PASSWORD', default=None)
MILVUS_DB_NAME = config('MILVUS_DB_NAME', default='milvus')
MILVUS_COLLECTION_NAME = config('MILVUS_COLLECTION_NAME', default='default')
MILVUS_OVERWRITE = config('MILVUS_OVERWRITE', default=False, cast=bool)

# 打印版本信息
APP_VERSION = version("theflow")

# Debug 信息（可选）
DEBUG = config('DEBUG', default=False, cast=bool)

RAG_LLMS = {}
RAG_EMBEDDINGS = {}
RAG_RERANKINGS = {}


def generate_local_model_path(model_name: str) -> str:
    """将模型名称转换为保存路径，例如 'BAAI/bge-reranker-base' 转为 'models--BAAI--bge-reranker-base'"""
    return str(RAG_MODEL_DIR / f"models--{model_name.replace('/', '--')}")


MODEL_SOURCE = config("MODEL_SOURCE", default="file")

# Embedding 和 Rerank 模型配置
EMBEDDING_MODEL_NAME = config("EMBEDDING_MODEL", default="TencentBAC/Conan-embedding-v1")
EMBEDDING_LOCAL_PATH = generate_local_model_path(EMBEDDING_MODEL_NAME)
RERANK_MODEL_NAME = config("RERANK_MODEL", default="BAAI/bge-reranker-base")
RERANK_LOCAL_PATH = generate_local_model_path(RERANK_MODEL_NAME)

if config("LOCAL_MODEL", default=""):
    RAG_LLMS["ollama"] = {
        "spec": {
            "__type__": "RAG.llms.chats.ollama.OllamaChatLLM",
            "base_url": config("OLLAMA_URL", default="http://localhost:11434/api").replace("/v1", "/api"),
            "model": config("LOCAL_MODEL", default="qwen2:7b"),
        },
        "default": True,
    }
    if MODEL_SOURCE == "ollama":
        RAG_EMBEDDINGS["ollama"] = {
            "spec": {
                "__type__": "RAG.embeddings.OpenAIEmbeddings",
                "base_url": "http://localhost:10434/v1/",
                "model": config("LOCAL_MODEL_EMBEDDINGS", default="nomic-embed-text"),
                "api_key": "ollama",
            },
            "default": False,
        }
    # elif MODEL_SOURCE == "file":
    #     RAG_EMBEDDINGS["fast_embed"] = {
    #         "spec": {
    #             "__type__": "RAG.embeddings.FastEmbedEmbeddings",
    #             "model_name": EMBEDDING_MODEL_NAME,
    #             "local_model_path": EMBEDDING_LOCAL_PATH,
    #         },
    #         "default": True,
    #     }
    elif MODEL_SOURCE == "file":
        RAG_EMBEDDINGS["st_embeddings"] = {
            "spec": {
                "__type__": "RAG.embeddings.STEmbeddings",
                "model_name": EMBEDDING_MODEL_NAME,
                "local_model_path": EMBEDDING_LOCAL_PATH,
            },
            "default": True,
        }
    if MODEL_SOURCE == "ollama":
        RAG_LLMS["baai"] = {
            "spec": {
                "__type__": "RAG.reranking.LocalBGEReranking",
                "base_url": "http://localhost:11434/v1/",
                "model": config("LOCAL_MODEL_EMBEDDINGS", default="BAAI/bge-reranker-base"),
                "api_key": "ollama",
            },
            "default": False,
        }
    elif MODEL_SOURCE == "file":
        RAG_RERANKINGS["baai"] = {
            "spec": {
                "__type__": "RAG.reranking.LocalBGEReranking",
                "model_name": RERANK_MODEL_NAME,
                "local_model_path": RERANK_LOCAL_PATH,
            },
            "default": True,
        }

RAG_REASONINGS = [
    "API.reasoning.simple.FullQAPipeline",
    "API.reasoning.simple.FullDecomposeQAPipeline",
    "API.reasoning.react.ReactAgentPipeline",
    "API.reasoning.rewoo.RewooAgentPipeline",
]
RAG_REASONINGS_USE_MULTIMODAL = False

SETTINGS_APP: dict[str, dict] = {}

DEFAULT_REASONING_MODE = "simple"
DEFAULT_LLM = "ollama"

SETTINGS_REASONING = {
    "use": {
        "name": "Reasoning options",
        "value": DEFAULT_REASONING_MODE,  # 设置默认值,
        "choices": ["simple", "complex", "ReAct", "ReWOO"],
        "component": "radio",
    },
    "lang": {
        "name": "Language",
        "value": "ch",
        "choices": [("Chinese", "ch"), ("English", "en")],
        "component": "dropdown",
    },
    "max_context_length": {
        "name": "Max context length (LLM)",
        "value": 32000,
        "component": "number",
    },
}


RAG_INDEX_TYPES = [
    "API.index.file.FileIndex",
    "API.index.file.graph.GraphRAGIndex",
]
RAG_INDICES = [
    {
        "name": "File",
        "config": {
            "supported_file_types": (
                ".png, .jpeg, .jpg, .tiff, .tif, .pdf, .xls, .xlsx, .doc, .docx, "
                ".pptx, .csv, .html, .mhtml, .txt, .md, .zip"
            ),
            "private": False,
        },
        "index_type": "API.index.file.FileIndex",
    },
    {
        "name": "GraphRAG",
        "config": {
            "supported_file_types": (
                ".png, .jpeg, .jpg, .tiff, .tif, .pdf, .xls, .xlsx, .doc, .docx, "
                ".pptx, .csv, .html, .mhtml, .txt, .md, .zip"
            ),
            "private": False,
        },
        "index_type": "API.index.file.graph.GraphRAGIndex",
    },
]

DOCUMENT_RETRIEVAL_SETTINGS = {
    "prioritize_table": False,
    "num_retrieval": 10,
    "mmr": False,
    "reader_mode": 'default',
    "retrieval_mode": "hybrid",
    "use_reranking": False,
    "use_llm_reranking": True,
    "reranking_llm": "default_llm_name"
}

# 和Java相关的其他配置
ALLOW_ORIGINS = config('ALLOW_ORIGINS', default='').replace(' ', '').split(',')
# print(f"ALLOW_ORIGINS inside flowsettings.py: {ALLOW_ORIGINS}")

# 默认chunk大小设置为512，可通过环境变量覆盖
CHUNK_SIZE = config("CHUNK_SIZE", default=640, cast=int)

# 表格相关配置
TABLE_CHUNK_SIZE = config("TABLE_CHUNK_SIZE", default=600, cast=int)  # 表格分片大小

# Redis配置
REDIS_HOST = config('REDIS_HOST', default='localhost')
REDIS_PORT = config('REDIS_PORT', default=6379, cast=int)
REDIS_DB_CACHE = 0  # 通用缓存
REDIS_DB_SESSION = 1  # 用户会话
REDIS_DB_RATE_LIMIT = 2  # 限流控制

# 线程池配置
CHAT_WORKER_THREAD_MIN = 1
CHAT_WORKER_THREAD_MAX = 2
FILE_WORKER_THREAD_MIN = 1
FILE_WORKER_THREAD_MAX = 2

# 监控配置
POOL_MONITOR_INTERVAL = 60    # 监控间隔（秒）
