<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test Page</title>
    <style>
        /* 保持原样 */
        #chat-history {
            border: 1px solid #ccc;
            padding: 10px;
            width: 80%;
            height: 300px;
            overflow-y: auto;
            margin-bottom: 15px;
        }
        .chat-message {
            white-space: pre-wrap;
            word-wrap: break-word;
            padding: 10px;
            margin: 5px;
            border-radius: 5px;
            font-family: monospace;  /* 使用等宽字体 */
        }
        .user-message {
            text-align: right;
            color: blue;
        }
        .bot-message {
            text-align: left;
            color: green;
            background-color: #f0f0f0;
            margin-left: 20px;
        }
    </style>
</head>
<body>
    <h1>API调试页面</h1>

    <h2>对话调试（综合）</h2>
    <div id="chat-history"></div>
    <form id="chat-form">
        <label for="chat-message">Message:</label>
        <input type="text" id="chat-message" name="message" required>
        <label for="chat-conversation-id">Conversation ID (Optional):</label>
        <input type="text" id="chat-conversation-id" name="conversation_id">
        <label for="user-id">User ID:</label>
        <input type="number" id="user-id" name="user_id" required>
        <button type="submit">Send Chat Message</button>
    </form>
    <pre id="chat-response"></pre>

    <h2>文档上传调试</h2>
    <form id="upload-form" enctype="multipart/form-data">
        <label for="upload-file">文件:</label>
        <input type="file" id="upload-file" name="file" required>

        <label for="upload-user-id">用户 ID:</label>
        <input type="number" id="upload-user-id" name="user_id" required>

        <label for="upload-reindex">重新索引:</label>
        <input type="checkbox" id="upload-reindex" name="reindex">

        <button type="submit">上传文件</button>
    </form>
    <pre id="upload-response"></pre>

    <script>
        document.getElementById('chat-form').addEventListener('submit', async (event) => {
            event.preventDefault();

            // 手动提取表单数据
            const message = document.getElementById('chat-message').value;
            const conversationId = document.getElementById('chat-conversation-id').value;
            const userId = document.getElementById('user-id').value;

            const requestData = {
                message: message,
                conversation_id: conversationId ? conversationId : null,
                user_id: parseInt(userId)
            };

            try {
                console.log('Sending request to /chat:', requestData);
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache',
                        'Connection': 'close'  // 强制关闭连接，避免复用
                    },
                    body: JSON.stringify(requestData)
                });

                console.log('Response received:', response.status, response.statusText);
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                console.log('Getting reader from response body');
                const reader = response.body.getReader();
                const chatHistory = document.getElementById('chat-history');
                const chatMessageElement = document.createElement('div');
                chatMessageElement.className = 'chat-message bot-message';
                chatHistory.appendChild(chatMessageElement);

                let resultBuffer = '';
                const decoder = new TextDecoder();
                console.log('Starting to read stream...');

                // 更新后的Markdown格式化函数
                function formatMarkdown(text) {
                    // 处理粗体
                    text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                    
                    // 处理段落：将多个换行符替换为两个 <br>
                    text = text.replace(/\n{2,}/g, '<br><br>');
                    
                    // 处理单个换行符
                    text = text.replace(/\n/g, '<br>'); // 处理单个换行符
                    
                    // 处理列表项
                    text = text.replace(/(\d+\. )/g, '<br>$1');

                    return text;
                }

                while (true) {
                    const {done, value} = await reader.read();
                    
                    if (done) {
                        console.log('Stream complete');
                        break;
                    }
                    
                    // 将新接收的数据转换为文本
                    const chunk = decoder.decode(value, {stream: true});
                    console.log('Received chunk:', chunk); // 调试日志
                    
                    // 将新数据添加到缓冲区
                    resultBuffer += chunk;

                    // 按照'\n\n'分割事件（Server-Sent Events的标准格式）
                    let parts = resultBuffer.split('\n\n');

                    // 处理所有完整的事件（除了最后一个可能不完整的）
                    for (let i = 0; i < parts.length - 1; i++) {
                        const event = parts[i];
                        console.log('Processing event:', event); // 调试日志

                        if (event.startsWith('data: ')) {
                            const data = event.slice(6).trim();
                            if (data !== '') {
                                console.log('Processing data:', data); // 调试日志
                                // 直接追加格式化后的HTML
                                chatMessageElement.innerHTML += formatMarkdown(data);
                                chatHistory.scrollTop = chatHistory.scrollHeight;
                            }
                        }
                    }

                    // 保留最后一个可能不完整的事件
                    resultBuffer = parts[parts.length - 1];
                }

                // 处理最后剩余的数据
                if (resultBuffer.trim() !== '') {
                    const event = resultBuffer.trim();
                    console.log('Processing final event:', event); // 调试日志
                    if (event.startsWith('data: ')) {
                        const data = event.slice(6).trim();
                        if (data !== '') {
                            console.log('Processing final data:', data); // 调试日志
                            chatMessageElement.innerHTML += formatMarkdown(data);
                            chatHistory.scrollTop = chatHistory.scrollHeight;
                        }
                    }
                }

                console.log('Stream processing completed'); // 调试日志
            } catch (error) {
                console.error(`请求出错: ${error.message}`);
                const chatHistory = document.getElementById('chat-history');
                const errorElement = document.createElement('div');
                errorElement.className = 'chat-message bot-message';
                errorElement.style.color = 'red';
                errorElement.textContent = `Error: ${error.message}`;
                chatHistory.appendChild(errorElement);
                chatHistory.scrollTop = chatHistory.scrollHeight;
            }
        });


        document.getElementById('upload-form').addEventListener('submit', async (event) => {
            event.preventDefault();

            const formData = new FormData(event.target);

            // 处理复选框的值，添加到 FormData 中（因为复选框没有被选中时不会被提交）
            const reindexCheckbox = document.getElementById('upload-reindex');
            formData.append('reindex', reindexCheckbox.checked);

            try {
                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                const result = await response.json();
                document.getElementById('upload-response').textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                // 增加错误信息的详细输出
                console.error('Upload error:', error);
                document.getElementById('upload-response').textContent = `Upload error: ${error.message}`;
            }
        });

    </script>
</body>
</html>
