import requests
import json
import logging
from typing import Optional, Iterator, List
from decouple import config

from RAG.base import BaseMessage, HumanMessage, LLMInterface, Param
from .base import ChatLLM

logger = logging.getLogger(__name__)

class OllamaChatLLM(ChatLLM):
    """Ollama 聊天模型的包装器"""

    base_url: str = Param(
        default=config("OLLAMA_URL", default="http://localhost:11434/api"),
        help="Ollama API 的基础 URL"
    )
    model: str = Param(
        help="要使用的 Ollama 模型名称",
        required=True
    )
    temperature: Optional[float] = Param(
        default=0.7,
        help="生成的随机性（0.0 到 1.0 之间）"
    )
    top_p: Optional[float] = Param(
        default=0.9,
        help="nucleus 采样的概率阈值"
    )
    max_tokens: Optional[int] = Param(
        default=None,
        help="生成的最大令牌数"
    )

    def prepare_message(
            self, messages: str | BaseMessage | List[BaseMessage]
    ) -> List[dict]:
        if isinstance(messages, str):
            return [{"role": "user", "content": messages}]
        elif isinstance(messages, BaseMessage):
            return [{"role": "user" if isinstance(messages, HumanMessage) else "system", "content": messages.content}]
        else:
            return [
                {
                    "role": "user" if isinstance(msg,
                                                 HumanMessage) else "system" if msg.type == "system" else "assistant",
                    "content": msg.content
                }
                for msg in messages
            ]

    def invoke(
            self, messages: str | BaseMessage | List[BaseMessage], **kwargs
    ) -> LLMInterface:
        prepared_messages = self.prepare_message(messages)

        # 确保base_url格式正确
        base_url = self.base_url.rstrip("/")
        if base_url.endswith("/api"):
            api_endpoint = f"{base_url}/chat"
        else:
            api_endpoint = f"{base_url}/api/chat"
            
        # logger.debug(f"Invoking Ollama. Base URL: {base_url}, Endpoint: {api_endpoint}")

        data = {
            "model": self.model,
            "messages": prepared_messages,
            "stream": False,
            "temperature": self.temperature,
            "top_p": self.top_p,
        }
        if self.max_tokens:
            data["max_tokens"] = self.max_tokens

        # logger.debug(f"Request data: {json.dumps(data)}")
        try:
            response = requests.post(api_endpoint, json=data)
            # logger.debug(f"Got response with status code: {response.status_code}")
            response.raise_for_status()
            result = response.json()
            
            return LLMInterface(
                content=result["message"]["content"],
                candidates=[result["message"]["content"]],
                # Ollama API 目前不提供 token 使用信息，所以这里设为 -1（而不是None）
                completion_tokens=-1,
                total_tokens=-1,
                prompt_tokens=-1,
            )
        except Exception as e:
            logger.error(f"Exception during Ollama invocation: {str(e)}")
            raise

    def stream(
            self, messages: str | BaseMessage | List[BaseMessage], **kwargs
    ) -> Iterator[LLMInterface]:
        prepared_messages = self.prepare_message(messages)
        
        # 确保base_url格式正确
        base_url = self.base_url.rstrip("/")
        if base_url.endswith("/api"):
            api_endpoint = f"{base_url}/chat"
        else:
            api_endpoint = f"{base_url}/api/chat"
            
        logger.info(f"Streaming request to Ollama. Base URL: {base_url}, Endpoint: {api_endpoint}")
        # logger.debug(f"Prepared messages: {prepared_messages}")

        data = {
            "model": self.model,
            "messages": prepared_messages,
            "stream": True,
            "temperature": self.temperature,
            "top_p": self.top_p,
        }
        if self.max_tokens:
            data["max_tokens"] = self.max_tokens
        
        # logger.debug(f"Request data: {json.dumps(data)}")
        
        try:
            logger.info(f"Sending POST request to {api_endpoint}")
            response = requests.post(api_endpoint, json=data, stream=True)
            logger.info(f"Got response with status code: {response.status_code}")
            response.raise_for_status()
            
            chunk_count = 0
            full_content = ""  # 用于累积所有响应内容
            response_done = False
            final_result = None
            
            for line in response.iter_lines():
                if line:
                    chunk = line.decode('utf-8')
                    # logger.debug(f"Received raw chunk: {chunk}")
                    
                    # 尝试直接解析JSON，不依赖"data: "前缀
                    try:
                        # 检查是否为[DONE]标记（虽然Ollama似乎不使用这种格式）
                        if chunk == '[DONE]':
                            response_done = True
                            continue
                            
                        # 解析JSON
                        result = json.loads(chunk)
                        # logger.debug(f"Parsed chunk result: {result}")
                        
                        # 保存最后一个结果，即使内容为空
                        if result.get("done", False) == True:
                            response_done = True
                            final_result = result
                        
                        content = result["message"]["content"]
                        # 收集所有内容，即使为空
                        full_content += content
                        
                        # 只有当内容不为空时才递增计数并生成输出
                        if content:
                            chunk_count += 1
                            # logger.debug(f"Adding chunk: '{content}', total so far: '{full_content}'")
                            yield LLMInterface(
                                content=content,
                                # 为流式输出也添加token计数默认值
                                completion_tokens=-1,
                                total_tokens=-1,
                                prompt_tokens=-1
                            )
                    except Exception as e:
                        logger.error(f"Error parsing chunk: {e}")
                        logger.error(f"Problematic chunk: {chunk}")
            
            logger.info(f"Stream completed. Total chunks received: {chunk_count}")
            logger.info(f"Full content accumulated: '{full_content}'")
            logger.info(f"Response done: {response_done}, Final result available: {final_result is not None}")
            
            # 如果响应完成但没有生成任何内容块，发送累积的全部内容
            if full_content and chunk_count == 0:
                logger.warning("Stream completed but no chunks were yielded individually. Yielding full accumulated content.")
                yield LLMInterface(
                    content=full_content,
                    completion_tokens=-1,
                    total_tokens=-1,
                    prompt_tokens=-1
                )
            # 如果没有任何内容被生成，但有最终结果，使用它
            elif chunk_count == 0 and final_result:
                final_content = final_result["message"]["content"]
                logger.warning(f"No content yielded. Using final result content: '{final_content}'")
                yield LLMInterface(
                    content=final_content or "无法获取模型回答",
                    completion_tokens=-1,
                    total_tokens=-1,
                    prompt_tokens=-1
                )
            # 如果没有任何内容被生成且没有最终结果，返回一个提示信息
            elif chunk_count == 0:
                logger.warning("No content received from Ollama API")
                yield LLMInterface(
                    content="模型未返回任何内容",
                    completion_tokens=-1,
                    total_tokens=-1,
                    prompt_tokens=-1
                )
                
        except Exception as e:
            logger.error(f"Exception during Ollama streaming: {str(e)}")
            # 即使发生异常，也确保生成器不为空
            yield LLMInterface(
                content=f"调用模型时发生错误: {str(e)}",
                completion_tokens=-1,
                total_tokens=-1,
                prompt_tokens=-1
            )
            raise
