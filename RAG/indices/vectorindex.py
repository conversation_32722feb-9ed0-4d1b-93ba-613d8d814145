from __future__ import annotations

from theflow.settings import settings as flowsettings
from RAG.base import BaseComponent, Document, RetrievedDocument
from RAG.embeddings import BaseEmbeddings
from RAG.storages import BaseDocumentStore, BaseVectorStore
from .base import BaseIndexing, BaseRetrieval
from .rankings import BaseReranking, LLMReranking

import threading
import uuid
from pathlib import Path
from typing import Optional, Sequence, cast
from logging import getLogger
logger = getLogger(__name__)

VECTOR_STORE_FNAME = "vectorstore"
DOC_STORE_FNAME = "docstore"


class VectorIndexing(BaseIndexing):
    """摄取文档，通过嵌入处理，并将嵌入存储在向量存储中。

    该管道支持以下输入集：
        - 文档列表
        - 文本列表
    """

    cache_dir: Optional[str] = getattr(flowsettings, "CHUNKS_OUTPUT_DIR", None)
    vector_store: BaseVectorStore
    doc_store: Optional[BaseDocumentStore] = None
    embedding: BaseEmbeddings
    count_: int = 0

    def to_retrieval_pipeline(self, *args, **kwargs):
        """将索引管道转换为检索管道"""
        return VectorRetrieval(
            vector_store=self.vector_store,
            doc_store=self.doc_store,
            embedding=self.embedding,
            **kwargs,
        )

    def to_qa_pipeline(self, *args, **kwargs):
        from .qa import CitationQAPipeline

        return TextVectorQA(
            retrieving_pipeline=self.to_retrieval_pipeline(**kwargs),
            qa_pipeline=CitationQAPipeline(**kwargs),
        )

    def write_chunk_to_file(self, docs: list[Document]):
        # 将块内容保存为 Markdown 格式
        if self.cache_dir:
            # file_name = Path(docs[0].metadata["file_name"])
            # 在循环外获取 file_name
            file_name = docs[0].metadata.get("file_name")
            if not file_name:
                # 如果没有 file_name，生成一个唯一的标识符
                file_name = f"document_{uuid.uuid4().hex[:8]}"

            file_stem = Path(file_name).stem

            for i in range(len(docs)):
                markdown_content = ""
                if "page_label" in docs[i].metadata:
                    page_label = str(docs[i].metadata["page_label"])
                    markdown_content += f"页标签: {page_label}"
                if "file_name" in docs[i].metadata:
                    filename = docs[i].metadata["file_name"]
                    markdown_content += f"\n文件名: {filename}"
                if "section" in docs[i].metadata:
                    section = docs[i].metadata["section"]
                    markdown_content += f"\n部分: {section}"
                if "type" in docs[i].metadata:
                    if docs[i].metadata["type"] == "image":
                        image_origin = docs[i].metadata["image_origin"]
                        image_origin = f'<p><img src="{image_origin}"></p>'
                        markdown_content += f"\n图像来源: {image_origin}"
                if docs[i].text:
                    markdown_content += f"\n文本:\n{docs[i].text}"

                # 如果没有任何内容，添加一个默认消息
                if not markdown_content:
                    markdown_content = "此文档没有可用的内容或元数据。"

                with open(
                    Path(self.cache_dir) / f"{file_stem}_{self.count_+i}.md",
                    "w",
                    encoding="utf-8",
                ) as f:
                    f.write(markdown_content)

    def add_to_docstore(self, docs: list[Document]):
        if self.doc_store:
            logger.debug("将文档添加到文档存储")

            # 确保文档的 ID 也转换为整数形式
            for doc in docs:
                logger.debug(f"Adding document to doc_store. ID: {doc.id_}, Type: {type(doc.id_)}")
                self.doc_store.add([doc])

    def add_to_vectorstore(self, docs: list[Document]):
        # 如果我们想跳过嵌入
        if self.vector_store:
            logger.debug(f"获取 {len(docs)} 个节点的嵌入")
            embeddings = self.embedding(docs)
            logger.debug("将嵌入添加到向量存储")
            doc_ids = [t.doc_id for t in docs]

            # 调试代码，查看 content 的长度信息
            content_lengths = [len(t.text) for t in docs if hasattr(t, 'text')]
            logger.debug(f"Document IDs being added to vectorstore: {doc_ids}")
            # logger.info(f"Content lengths: {content_lengths}")

            self.vector_store.add(
                embeddings=embeddings,
                ids=doc_ids,  # 使用与 doc_store 一致的 ID
            )

    def run(self, text: str | list[str] | Document | list[Document]):
        input_: list[Document] = []
        if not isinstance(text, list):
            text = [text]

        for item in cast(list, text):
            logger.debug(f"Processing item of type: {type(item)}")
            if isinstance(item, str):
                str_id = str(uuid.uuid4())
                logger.debug(f"Created new id for string: {str_id}")
                input_.append(Document(text=item, id_=str_id))
            elif isinstance(item, Document):
                logger.debug(f"Document id before processing: {item.id_}, type: {type(item.id_)}")
                if item.id_ is None:
                    item.id_ = str(uuid.uuid4())
                else:
                    item.id_ = str(item.id_)
                logger.debug(f"Document id after processing: {item.id_}, type: {type(item.id_)}")
                input_.append(item)
            else:
                raise ValueError(f"无效的输入类型 {type(item)}，应为 str 或 Document")

        self.add_to_vectorstore(input_)
        self.add_to_docstore(input_)
        self.write_chunk_to_file(input_)
        self.count_ += len(input_)


class VectorRetrieval(BaseRetrieval):
    """从向量存储中检索文档列表"""

    vector_store: BaseVectorStore
    doc_store: Optional[BaseDocumentStore] = None
    embedding: BaseEmbeddings
    rerankers: Sequence[BaseReranking] = []
    top_k: int = 5
    first_round_top_k_mult: int = 3
    retrieval_mode: str = "vector"  # vector, text, hybrid

    def _filter_docs(
        self, documents: list[RetrievedDocument], top_k: int | None = None
    ):
        if top_k:
            documents = documents[:top_k]
        return documents

    def run(
        self, text: str | Document, top_k: Optional[int] = None, **kwargs
    ) -> list[RetrievedDocument]:
        # logger.debug(f"Running VectorRetrieval with vector_store type: {type(self.vector_store)}")
        # logger.debug(f"Vector store attributes: {dir(self.vector_store)}")
        # logger.debug(f"Vector store query method: {getattr(self.vector_store, 'query', 'Not found')}")
        """从向量存储中检索文档列表

        参数:
            text: 用于检索相似文档的文本
            top_k: 返回的相似文档数量

        返回:
            list[RetrievedDocument]: 检索到的文档列表
        """
        logger.info(f"VectorRetrieval text: {text}, self.retrieval_mode: {self.retrieval_mode}")
        if top_k is None:
            top_k = self.top_k

        do_extend = kwargs.pop("do_extend", False)
        thumbnail_count = kwargs.pop("thumbnail_count", 3)

        if do_extend:
            top_k_first_round = top_k * self.first_round_top_k_mult
        else:
            top_k_first_round = top_k

        if self.doc_store is None:
            raise ValueError(
                "未提供 doc_store。请提供一个 doc_store 以检索文档"
            )

        result: list[RetrievedDocument] = []
        # TODO: 应在 run 参数中直接声明 scope
        scope = kwargs.pop("scope", None)
        emb: list[float]

        if self.retrieval_mode == "vector":
            emb = self.embedding(text)[0].embedding
            _, scores, filenames, ids = self.vector_store.query(embedding=emb, top_k=top_k_first_round, **kwargs)
            docs = self.doc_store.get(ids)
            result = [
                RetrievedDocument(**doc.to_dict(), score=score)
                for doc, score in zip(docs, scores)
            ]
        elif self.retrieval_mode == "text":
            query = text.text if isinstance(text, Document) else text
            docs = self.doc_store.query(query, top_k=top_k_first_round, doc_ids=scope)
            result = [RetrievedDocument(**doc.to_dict(), score=-1.0) for doc in docs]
        elif self.retrieval_mode == "hybrid":
            # 相似性搜索部分
            try:
                # logger.info(f"运行向量检索，输入文本长度：{len(text)}")
                emb = self.embedding(text)[0].embedding
                # logger.info(f"嵌入结果维度: {len(emb) if emb is not None else 'None'}")
            except Exception as e:
                logger.info(f"VectorRetrieval run 方法中的异常: {e}")
                # logger.info(f"异常类型: {type(e)}")
                # logger.info(f"异常详细信息: {e.args}")
                raise e
            vs_docs: list[RetrievedDocument] = []
            vs_ids: list[str] = []
            vs_scores: list[float] = []

            def query_vectorstore():
                nonlocal vs_docs
                nonlocal vs_scores
                nonlocal vs_ids

                # logger.info(f"About to call query on {type(self.vector_store)}")
                # logger.info(f"Query method: {self.vector_store.query}")

                assert self.doc_store is not None
                vs_scores, vs_ids = self.vector_store.query(
                    embedding=emb, top_k=top_k_first_round, **kwargs
                )
                if vs_ids:
                    vs_docs = self.doc_store.get(vs_ids)

                # 添加日志以查看检索到的有效文档 ID
                logger.debug(f"Filtered valid vector store document IDs: {vs_ids}")

            # 全文搜索部分
            ds_docs: list[RetrievedDocument] = []

            def query_docstore():
                nonlocal ds_docs

                assert self.doc_store is not None
                query = text.text if isinstance(text, Document) else text
                ds_docs = self.doc_store.query(
                    query, top_k=top_k_first_round, doc_ids=scope
                )

            vs_query_thread = threading.Thread(target=query_vectorstore)
            ds_query_thread = threading.Thread(target=query_docstore)

            vs_query_thread.start()
            ds_query_thread.start()

            vs_query_thread.join()
            ds_query_thread.join()

            result = [
                RetrievedDocument(**doc.to_dict(), score=-1.0)
                for doc in ds_docs
                if doc not in vs_ids
            ]
            result += [
                RetrievedDocument(**doc.to_dict(), score=score)
                for doc, score in zip(vs_docs, vs_scores)
            ]
            for doc in result:
                content = doc.content
                score = doc.score
                # logger.info(f"{content[:10]} - {score}")
            logger.debug(f"从向量存储中获取 {len(vs_docs)} 个文档")
            logger.debug(f"从文档存储中获取 {len(ds_docs)} 个文档")

        # 使用额外的重排序器重新排序文档列表
        if self.rerankers and text:
            for reranker in self.rerankers:
                # 如果重排序器是 LLMReranking，仅限制 top_k 个文档
                if isinstance(reranker, LLMReranking):
                    result = self._filter_docs(result, top_k=top_k)
                result = reranker(documents=result, query=text)

        result = self._filter_docs(result, top_k=top_k)
        logger.debug(f"获取到 {len(result)} 个检索到的文档")

        # 如果存在，将页面缩略图添加到结果中
        thumbnail_doc_ids: set[str] = set()
        # 我们应该从检索到的文本块中复制文本
        # 到缩略图以正确获取相关的 LLM 分数
        text_thumbnail_docs: dict[str, RetrievedDocument] = {}

        non_thumbnail_docs = []
        raw_thumbnail_docs = []
        for doc in result:
            if doc.metadata.get("type") == "thumbnail":
                # 将类型更改为图像以在 UI 上显示
                doc.metadata["type"] = "image"
                raw_thumbnail_docs.append(doc)
                continue
            if (
                "thumbnail_doc_id" in doc.metadata
                and len(thumbnail_doc_ids) < thumbnail_count
            ):
                thumbnail_id = doc.metadata["thumbnail_doc_id"]
                thumbnail_doc_ids.add(thumbnail_id)
                text_thumbnail_docs[thumbnail_id] = doc
            else:
                non_thumbnail_docs.append(doc)

        linked_thumbnail_docs = self.doc_store.get(list(thumbnail_doc_ids))
        logger.debug(f"缩略图文档: {len(linked_thumbnail_docs)}, 非缩略图文档: {len(non_thumbnail_docs)}, "
                     f"原始缩略图文档: {len(raw_thumbnail_docs)}")
        additional_docs = []

        for thumbnail_doc in linked_thumbnail_docs:
            text_doc = text_thumbnail_docs[thumbnail_doc.doc_id]
            doc_dict = thumbnail_doc.to_dict()
            doc_dict["_id"] = text_doc.doc_id
            doc_dict["content"] = text_doc.content
            doc_dict["metadata"]["type"] = "image"
            for key in text_doc.metadata:
                if key not in doc_dict["metadata"]:
                    doc_dict["metadata"][key] = text_doc.metadata[key]

            additional_docs.append(RetrievedDocument(**doc_dict, score=text_doc.score))

        result = additional_docs + non_thumbnail_docs

        if not result:
            # 返回原始检索到的缩略图
            result = self._filter_docs(raw_thumbnail_docs, top_k=thumbnail_count)

        return result


class TextVectorQA(BaseComponent):
    retrieving_pipeline: BaseRetrieval
    qa_pipeline: BaseComponent

    def run(self, question, **kwargs):
        retrieved_documents = self.retrieving_pipeline(question, **kwargs)
        return self.qa_pipeline(question, retrieved_documents, **kwargs)