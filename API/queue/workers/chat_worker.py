import logging
import asyncio
import time
from typing import Dict, Any
from fastapi import HTTPException
from API.queue.task_manager import TaskManager, TaskStatus
from API.services.chat_service import ChatService
from asyncio import Queue

logger = logging.getLogger(__name__)


class ChatWorker:
    def __init__(self, task_manager: TaskManager, chat_service: ChatService):
        """
        聊天任务工作者，用于处理聊天任务
        Args:
            task_manager (TaskManager): 任务队列实例
            chat_service (ChatService): ChatService实例
        """
        self.task_manager = task_manager
        self.chat_service = chat_service
        # 移除实例级别的response_queue，改为任务级别
        logger.info("ChatWorker initialized with TaskManager and ChatService")

    async def process_chat_task(self, task_id: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单个聊天任务
        Args:
            task_id (str): 任务唯一 ID
            payload (dict): 任务的载荷数据，包含 message, user_id, chat_history 等
        """
        try:
            start_time = time.time()
            # 为当前任务创建独立的response_queue（必须在注册worker之前创建）
            self.response_queue = asyncio.Queue()
            logger.debug(f"Created new response_queue for task {task_id}")

            # 注册当前worker实例
            self.task_manager.register_worker(task_id, self)
            logger.debug(f"Registered worker for task {task_id}")
            
            logger.info(f"ChatWorker - Processing task {task_id}")
            logger.debug(f"Task payload: {payload}")  # 详细信息移到debug级别

            # 1. 提取任务参数
            message = payload.get("message")
            user_id = payload.get("user_id")
            chat_history = payload.get("chat_history", [])
            conversation_id = payload.get("conversation_id", None)
            conversation_name = payload.get("conversation_name", None)

            if not message or not user_id:
                logger.error(f"Invalid chat task payload for task {task_id}")
                raise ValueError("Invalid chat task payload: missing required fields")

            # 2. 调用 submit_msg
            msg_response = await self.chat_service.submit_msg(
                chat_input=message,
                chat_history=chat_history,
                user_id=user_id,
                conv_id=conversation_id,
                conv_name=conversation_name
            )

            # 3. 获取默认设置
            settings = self.chat_service._app.default_settings.flatten()
            state = {"app": {}, "pipeline": {}}

            # 4. 获取独立的pipeline实例并处理对话
            async for response in self.chat_service.chat_fn(
                msg_response["conv_id"],
                msg_response["chat_history"],
                settings=settings,
                reasoning_type=None,
                llm_type=None,
                state=state,
                user_id=user_id
            ):
                # logger.debug(f"Received response in chat_worker: {response}")
                if response:
                    # 安全地获取response的字符串表示用于日志
                    response_str = str(response)
                    logger.debug(f"Putting response to queue for task {task_id}: {response_str[:50]}...")
                    await self.response_queue.put({
                            "task_id": task_id,
                            "response": response,
                            "is_complete": False
                        })
                    logger.debug(f"Response put to queue successfully for task {task_id}")

            # 标记完成
            end_time = time.time()
            logger.info(f"Chat task with {message} completed, marking as complete, time: {end_time - start_time}")
            logger.debug(f"Putting completion marker to queue for task {task_id}")
            await self.response_queue.put({
                "task_id": task_id,
                "response": None,
                "is_complete": True
            })
            logger.debug(f"Completion marker put to queue successfully for task {task_id}")

            return msg_response

        except Exception as e:
            logger.error(f"ChatWorker - Task {task_id} failed: {str(e)}")
            # 确保在发生错误时也标记完成（如果response_queue已创建）
            if hasattr(self, 'response_queue'):
                try:
                    await self.response_queue.put({
                        "task_id": task_id,
                        "response": None,
                        "is_complete": True,
                        "error": str(e)
                    })
                except Exception as queue_error:
                    logger.error(f"Failed to put error message to queue: {queue_error}")
            self.task_manager.update_task_status(task_id, TaskStatus.FAILED, str(e))
            raise

        except TimeoutError as te:
            # 针对超时异常返回408错误
            raise HTTPException(status_code=408, detail=f"Timeout Error: {str(te)}")
            
        finally:
            # 确保在任务完成后移除worker注册和清理队列
            logger.debug(f"Remove worker: {task_id}")
            self.task_manager.remove_worker(task_id)
            # 清理当前任务的response_queue
            if hasattr(self, 'response_queue'):
                # 清空队列中的剩余数据
                while not self.response_queue.empty():
                    try:
                        self.response_queue.get_nowait()
                    except asyncio.QueueEmpty:
                        break
                logger.debug(f"Cleaned up response_queue for task {task_id}")
