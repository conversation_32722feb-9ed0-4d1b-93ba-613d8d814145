from fastapi import FastAPI, APIRouter, UploadFile, File, Form, Request, HTTPException, Response
from typing import Optional
from pathlib import Path
from fastapi.responses import StreamingResponse, JSONResponse
from redis import Redis
import json
import time
import asyncio
import logging

from API.app import BaseApp
from API.services.chat_service import ChatService
from API.pages.chat.conv_service import ConvService
from API.queue.workers.file_worker import FileWorker
from API.queue.workers.chat_worker import ChatWorker
from API.queue.task_manager import TaskManager, TaskStatus, TaskType
from theflow.settings import settings as flowsettings

# 定义路由
router = APIRouter()

# 添加全局OPTIONS处理器，处理所有路径的OPTIONS请求
@router.options("/{path:path}")
async def global_options_handler(request: Request, path: str):
    """
    全局OPTIONS请求处理器，确保所有路径的OPTIONS请求都能被正确处理
    """
    logger.debug(f"Handling OPTIONS request for path: /{path}")
    # 记录请求的详细信息，帮助调试
    origin = request.headers.get("origin")
    method = request.headers.get("access-control-request-method")
    headers = request.headers.get("access-control-request-headers")
    logger.debug(f"OPTIONS request details - Origin: {origin}, Method: {method}, Headers: {headers}")
    
    # 返回200状态码，让FastAPI的CORS中间件处理响应头
    return Response(status_code=200)

# 设置应用运行模式和数据存在标识
RAG_DEMO_MODE = getattr(flowsettings, "RAG_DEMO_MODE", False)
RAG_APP_DATA_EXISTS = getattr(flowsettings, "RAG_APP_DATA_EXISTS", True)

logger = logging.getLogger(__name__)


class App(BaseApp):
    """RAG主要应用类，改造为通过 FastAPI 提供服务

    包含应用的基本信息和方法：
        - 初始化核心功能
        - 保留 Chat 测试页以测试 API 功能
    """
    _instance = None  # 单例实例

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(App, cls).__new__(cls, *args, **kwargs)
        return cls._instance

    def __init__(self):
        super().__init__()
        self.conv_service = ConvService(self)  # 实例化 ConvService，用于后续的 API 服务
        self.chat_service = ChatService(self, self.conv_service)  # 实例化 ChatService，用于后续的 API 服务

        # 初始化 Redis 客户端（这里假设你已经有了 Redis 配置）
        redis_client = Redis(
            host=flowsettings.REDIS_HOST,
            port=flowsettings.REDIS_PORT,
            db=flowsettings.REDIS_DB_CACHE
        )

        self.task_manager = TaskManager(redis_client=redis_client)

        # 配置文件任务消费者
        self.file_worker = FileWorker(task_manager=self.task_manager)
        self.file_worker.index_manager = self.index_manager

        self.task_manager.thread_pool_manager.register_worker(
            TaskType.FILE.value,
            self.file_worker.process_file_task
        )

        # 配置聊天任务消费者
        self.chat_worker = ChatWorker(
            task_manager=self.task_manager,
            chat_service=self.chat_service
        )

        self.task_manager.thread_pool_manager.register_worker(
            TaskType.CHAT.value,
            self.chat_worker.process_chat_task
        )

        # 启动队列监控
        self.task_manager.thread_pool_manager.start_queue_monitor(flowsettings.POOL_MONITOR_INTERVAL)

    def register_routes(self, app: FastAPI):
        """注册 FastAPI 的路由，提供不同的功能作为 API 服务"""

        @router.post("/chat")
        async def chat_endpoint(request: Request):
            """
            综合对话接口，接受用户的问题并返回回答。
            参数：
                message (str): 用户输入的消息
                conversation_id (Optional[int]): 可选的会话 ID
                user_id (int): 用户 ID
                conversation_name (Optional[str]): 可选的会话名称
            返回：
                StreamingResponse: 包含聊天回复的信息，逐步返回给前端
            """

            try:
                data = await request.json()
                logger.debug(f"Received request with data: {json.dumps(data, indent=2)}")
                logger.debug(f"Request headers: {dict(request.headers)}")

                message = data.get("message")
                user_id = data.get("user_id")
                conversation_id = data.get("conversation_id")
                conversation_name = data.get("conversation_name")

                # 详细的参数验证日志
                logger.debug(f"Validating parameters - message: {message}, user_id: {user_id}")

                if not message or not user_id:
                    error_msg = f"Missing required fields: message={'missing' if not message else 'present'}, " \
                                f"user_id={'missing' if not user_id else 'present'}"
                    logger.debug(f"Validation failed: {error_msg}")
                    raise HTTPException(status_code=400, detail=error_msg)

                origin = request.headers.get('origin')
                logger.debug(f"Received request from origin: {origin}")

                # 创建聊天任务
                task_id = self.task_manager.create_task(
                    task_type=TaskType.CHAT.value,
                    task_data={
                        "message": message,
                        "user_id": user_id,
                        "chat_history": [],
                        "conversation_id": conversation_id,
                        "conversation_name": conversation_name
                    }
                )

                async def response_stream():
                    try:
                        # 等待worker就绪，不设置超时
                        worker = None
                        try:
                            worker = await self.task_manager.wait_for_worker(task_id)
                        except ValueError as e:
                            logger.error(f"Task error: {str(e)}")
                            yield f"data: Error: {str(e)}\n\n"
                            return
                        except Exception as e:
                            logger.error(f"Unexpected error while waiting for worker: {str(e)}")
                            yield f"data: Error: An unexpected error occurred\n\n"
                            return

                        logger.debug(f"Starting response stream for task {task_id}")
                        response_count = 0
                        while True:
                            try:
                                logger.debug(f"Waiting for response data from queue for task {task_id}")
                                response_data = await worker.response_queue.get()
                                response_count += 1
                                logger.debug(f"Received response data #{response_count} for task {task_id}: is_complete={response_data.get('is_complete', False)}")

                                if response_data["task_id"] != task_id:
                                    logger.warning(f"Received response for different task: expected {task_id}, got {response_data['task_id']}")
                                    continue

                                if response_data["is_complete"]:
                                    logger.debug(f"Task {task_id} marked as complete after {response_count} responses, ending stream")
                                    break

                                response = response_data.get("response")
                                if response:
                                    # 安全地获取response的字符串表示
                                    response_str = str(response)
                                    if "\n" in response_str:
                                        logger.debug(f"Yielding response with newline for task {task_id}")
                                    else:
                                        logger.debug(f"Yielding response for task {task_id}: {response_str[:50]}...")
                                    yield f"data: {response_str}\n\n"
                                    logger.debug(f"Response yielded successfully for task {task_id}")

                                worker.response_queue.task_done()
                            except Exception as e:
                                logger.error(f"Error in response stream for task {task_id}: {str(e)}")
                                break

                        logger.debug(f"Response stream ended for task {task_id}")

                    except Exception as e:
                        logger.error(f"Error in response stream: {str(e)}")
                        raise HTTPException(status_code=500, detail=str(e))

                return StreamingResponse(
                    response_stream(),
                    media_type="text/event-stream"
                )

            except Exception as e:
                logger.error(f"Error in chat_endpoint: {e}")
                raise HTTPException(status_code=500, detail=str(e))

        @router.post("/upload")
        async def upload_file_endpoint(
                request: Request,
                file: UploadFile = File(...),
                user_id: int = Form(...),
                reindex: Optional[bool] = Form(False)
        ):
            """
            上传文件接口，用于处理用户上传的文件并将其进行索引。
            """
            # 添加调试日志
            logger.debug("DEBUG: Received /upload request")
            logger.debug(f"DEBUG: User ID: {user_id}, Reindex: {reindex}")
            logger.debug(f"DEBUG: Uploaded file name: {file.filename}, Content type: {file.content_type}")
            logger.debug(f"Request headers: {request.headers}")
            logger.debug(f"Request form data: {await request.form()}")

            try:
                # 保存上传的文件到服务器的临时位置
                temp_dir = Path("app_data/tmp")
                temp_dir.mkdir(parents=True, exist_ok=True)
                saved_file_path = temp_dir / file.filename

                with open(saved_file_path, "wb") as f:
                    f.write(await file.read())

                # 提交任务到文件处理队列
                task_id = self.task_manager.create_task(
                    task_type=TaskType.FILE.value,
                    task_data={
                        "file_paths": [str(saved_file_path)],
                        "reindex": reindex,
                        "settings": {},  # 可以根据需要添加其他设置
                        "user_id": user_id  # 添加user_id到任务数据中
                    }
                )

                if not task_id:
                    raise HTTPException(status_code=500, detail="Failed to enqueue the file task")

                # 等待任务状态更新
                while True:
                    task_status = self.task_manager.get_task_status(task_id)
                    if task_status.get('status') == TaskStatus.COMPLETED:
                        return {"status": "已完成", "file_name": file.filename}
                    elif task_status.get('status') == TaskStatus.FAILED:
                        raise HTTPException(status_code=500, detail=task_status.get('error', 'Task failed'))
                    await asyncio.sleep(0.1)

            except HTTPException as e:
                # 如果是 HTTPException，则直接返回响应
                return JSONResponse(content={"detail": e.detail}, status_code=e.status_code)

            except Exception as e:
                logger.info(f"Error in upload_file_endpoint: {str(e)}")
                return JSONResponse(
                    status_code=500,
                    content={"code": -1, "msg": f"Internal Server Error: {str(e)}", "data": {}}
                )

        # 注册更多路由和功能，这里可以扩展为支持文件索引、检索等服务
        app.include_router(router)
