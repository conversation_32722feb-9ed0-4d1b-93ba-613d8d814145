#!/usr/bin/env python3
"""
测试流式响应的脚本
用于验证后端的流式响应是否正常工作
"""

import asyncio
import aiohttp
import json
import sys

async def test_streaming_chat(question: str, test_name: str):
    """测试单个问答的流式响应"""
    print(f"\n=== {test_name} ===")
    print(f"问题: {question}")
    
    url = "http://192.168.3.172:8000/chat"
    data = {
        "message": question,
        "user_id": 1
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url, 
                json=data,
                headers={'Content-Type': 'application/json'}
            ) as response:
                print(f"HTTP状态: {response.status}")
                
                if response.status != 200:
                    print(f"错误: {await response.text()}")
                    return False
                
                # 读取流式响应
                response_parts = []
                async for line in response.content:
                    line_str = line.decode('utf-8')
                    if line_str.strip():
                        print(f"收到数据: {repr(line_str[:100])}...")
                        if line_str.startswith('data: '):
                            content = line_str[6:].strip()
                            if content:
                                response_parts.append(content)
                
                print(f"总共收到 {len(response_parts)} 个数据块")
                if response_parts:
                    print(f"完整回答: {''.join(response_parts)}")
                    return True
                else:
                    print("没有收到任何数据!")
                    return False
                    
    except Exception as e:
        print(f"请求失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("开始测试流式响应...")
    
    # 测试问题列表
    questions = [
        "离线检测数据达到停运值时处置原则是什么？",
        "乙炔的注意值1，注意值2，告警值和停运值分别是多少",
        "特高压换流变离线油色谱检测取样要求是什么样的？"
    ]
    
    results = []
    for i, question in enumerate(questions, 1):
        result = await test_streaming_chat(question, f"测试 {i}")
        results.append(result)
        
        # 等待一段时间再进行下一个测试
        if i < len(questions):
            print("等待5秒后进行下一个测试...")
            await asyncio.sleep(5)
    
    # 总结结果
    print(f"\n=== 测试总结 ===")
    for i, result in enumerate(results, 1):
        status = "成功" if result else "失败"
        print(f"测试 {i}: {status}")
    
    success_count = sum(results)
    print(f"成功: {success_count}/{len(results)}")
    
    if success_count == len(results):
        print("所有测试都成功！后端流式响应正常。")
    else:
        print("有测试失败，可能存在问题。")

if __name__ == "__main__":
    asyncio.run(main())
